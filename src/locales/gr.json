{"navigation": {"dashboard": "Πίνα<PERSON><PERSON><PERSON>λ<PERSON>γχου", "revenue": "Έσοδα", "demographics": "Δημογρα<PERSON><PERSON><PERSON><PERSON>ατών", "competition": "Ανταγωνισμός"}, "header": {"title": "NBG Business Insights", "language": "Γλώσσα"}, "filters": {"title": "Φίλτρα", "dateRange": "Χρονικ<PERSON> Διάστημα", "channel": "Κανάλι", "demographics": "Δημογραφικά", "gender": "Φύλο", "ageGroup": "Ηλικιακή Ομάδα", "customerLocation": "Τοποθεσία Πελάτη", "goForMore": "<PERSON>ελ<PERSON><PERSON><PERSON><PERSON> Go For More", "shoppingInterests": "Ενδιαφέροντα Αγορών", "store": "Κατάστημα", "apply": "Εφαρμογή <PERSON>λτρων", "reset": "Επαναφορά"}, "channels": {"all": "Όλα", "physical": "Φυσικ<PERSON> Καταστήματα", "ecommerce": "Ηλεκτρονικό Εμπόριο"}, "genders": {"all": "Όλα", "male": "Άνδρες", "female": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ς"}, "ageGroups": {"all": "Όλα", "genZ": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> (18-24)", "millennials": "Millennials (25-40)", "genX": "Γενιά X (41-56)", "boomers": "<PERSON> Boom<PERSON> (57-75)", "silent": "Σιω<PERSON><PERSON><PERSON><PERSON> (76-96)"}, "goForMoreOptions": {"yes": "Ναι", "no": "Όχι"}, "dashboard": {"title": "Πίνα<PERSON><PERSON><PERSON> Μετρήσεων", "subtitle": "Επισκόπη<PERSON><PERSON> όγκων συναλλαγών για την επιλεγμένη χρονική περίοδο", "merchant": "Έμπορος", "competition": "Ανταγωνισμός", "totalTransactions": "Συνολικές Συναλλαγές", "totalRevenue": "Συνολικά Έσοδα", "avgTransaction": "Μέσο Ποσό ανά Συναλλαγή", "customers": "Πελάτες", "transactions": "Συναλλαγ<PERSON>ς", "revenue": "Έσοδα", "vsLastYear": "σε σχέση με πέρυσι", "vsCompetition": "σε σχέση με ανταγωνισμό", "changeFromLastYear": "Μεταβολή από πέρυσι"}, "revenue": {"title": "Αναλυτικ<PERSON>σ<PERSON>δων", "subtitle": "<PERSON><PERSON><PERSON> βά<PERSON>ος ανάλυ<PERSON>η εσόδων για τον έμπορο και τον ανταγωνισμό", "totalRevenue": "Συνολικά Έσοδα", "avgDailyRevenue": "Μέσα Ημερήσια Έσοδα", "avgTransaction": "Μέσο Ποσό ανά Συναλλαγή", "goForMoreRevenue": "Συνολικά Έσοδα από Go For More", "goForMoreRewarded": "Συνολικό Ποσό Ανταμοιβών από Go For More", "goForMoreRedeemed": "Συνολικό Ποσό Εξαργύρωσης από Go For More", "revenueTrend": "Τάση Εσόδων", "revenueChange": "Μεταβολή Εσόδων % από Πέρυσι", "byShoppingInterests": "Έσοδα ανά Ενδιαφέροντα Αγορών", "byChannel": "Έσοδα ανά <PERSON>λι"}, "demographics": {"title": "Αναλυτικ<PERSON>ελατών", "subtitle": "Δημογρα<PERSON><PERSON><PERSON><PERSON> και συμπεριφορικά πρότυπα πελατών", "totalCustomers": "Συνολικο<PERSON> Πελάτες", "newCustomers": "Νέοι Πελάτες", "returningCustomers": "Επανα<PERSON><PERSON><PERSON><PERSON><PERSON>νοντες Πελάτες", "topSpenders": "Κορυφαίοι Πελάτες", "loyalCustomers": "Πιστοί Πελάτες", "atRiskCustomers": "Πελάτες σε Κίνδυνο", "customersByGender": "Πελά<PERSON><PERSON><PERSON> αν<PERSON>ο", "customersByAge": "Πελά<PERSON><PERSON><PERSON> ανά Ηλικιακή Ομάδα", "customersByFrequency": "Π<PERSON>λ<PERSON><PERSON><PERSON><PERSON> αν<PERSON>νότητα Αγορών", "customersByInterests": "Πελ<PERSON><PERSON><PERSON><PERSON> ανά <PERSON>νδιαφέροντα Αγορών"}, "competition": {"title": "Αναλυτι<PERSON><PERSON> Ανταγωνισμού", "subtitle": "Σύγκριση με τον Ανταγωνισμό για την επιλεγμένη χρονική περίοδο", "revenue": "Έσοδα", "transactions": "Συναλλαγ<PERSON>ς", "avgTransactionAmount": "Μέσο Ποσ<PERSON>λλαγής", "comparedToLastYear": "Σε σχέση με πέρυσι", "comparedToCompetition": "Σε σχέση με τον ανταγωνισμό", "merchantVsCompetition": "Έμπορος vs Ανταγωνισμός", "competitorChange": "Μεταβολή Ανταγωνισμού", "weeklyTurnover": "Εβδομαδιαία Τάση Τζίρου", "monthlyTurnover": "Μηνι<PERSON><PERSON><PERSON>ρο<PERSON><PERSON><PERSON>ιο Τζίρου"}, "chartOptions": {"bars": "Μπάρες", "line": "Γραμμή", "table": "Π<PERSON>ν<PERSON><PERSON><PERSON>ς", "daily": "Ημερήσια", "weekly": "Εβδομαδιαία", "monthly": "Μηνιαία", "quarterly": "Τριμηνιαία", "yearly": "Ετήσια"}, "common": {"loading": "Φόρτωση...", "noData": "Δεν υπάρχουν δεδομένα", "error": "Παρου<PERSON><PERSON>ά<PERSON>τηκε σφάλμα", "percentage": "%", "currency": "€", "close": "Κλείσιμο", "open": "Άνοιγμα", "merchantDataOnly": "Δεδομένα εμπόρου μόνο (απαίτηση συμμόρφωσης)"}}